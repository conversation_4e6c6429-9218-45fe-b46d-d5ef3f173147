#!/usr/bin/env python3
"""
测试MSA-CNN数据预处理脚本
处理单个受试者数据：SC4002E0
"""

import os
import sys
import glob
import numpy as np
from datetime import datetime
from mne.io import read_raw_edf

# 添加data_handler到Python路径
sys.path.append("MSA-CNN/data_handler")
from cleaning import dhedfreader

# 从原脚本复制的常量和映射
W = 0
N1 = 1
N2 = 2
N3 = 3
REM = 4
UNKNOWN = 5

ann2label = {
    "Sleep stage W": 0,
    "Sleep stage 1": 1,
    "Sleep stage 2": 2,
    "Sleep stage 3": 3,
    "Sleep stage 4": 3,
    "Sleep stage R": 4,
    "Sleep stage ?": 5,
    "Movement time": 5,
}

EPOCH_SEC_SIZE = 30


def test_single_subject():
    """测试处理单个受试者SC4002E0"""

    print("=== 测试MSA-CNN数据预处理流程 ===")
    print("目标受试者: SC4002E0")
    print()

    # 配置数据路径
    dataset_config = {
        "data_dir": "MSA-CNN/data_handler/data/Sleep_EDF_20/edf_files/",
        "output_dir": "test_output/",
    }

    # 创建输出目录
    if not os.path.exists(dataset_config["output_dir"]):
        os.makedirs(dataset_config["output_dir"])

    print(f"输入目录: {dataset_config['data_dir']}")
    print(f"输出目录: {dataset_config['output_dir']}")
    print()

    # 检查输入文件是否存在
    psg_file = os.path.join(dataset_config["data_dir"], "SC4002E0-PSG.edf")
    ann_file = os.path.join(dataset_config["data_dir"], "SC4002EC-Hypnogram.edf")

    print("检查输入文件:")
    print(f"PSG文件: {psg_file}")
    print(f"  存在: {os.path.exists(psg_file)}")
    if os.path.exists(psg_file):
        print(f"  大小: {os.path.getsize(psg_file) / 1024 / 1024:.1f} MB")

    print(f"Hypnogram文件: {ann_file}")
    print(f"  存在: {os.path.exists(ann_file)}")
    if os.path.exists(ann_file):
        print(f"  大小: {os.path.getsize(ann_file) / 1024:.1f} KB")
    print()

    if not (os.path.exists(psg_file) and os.path.exists(ann_file)):
        print("❌ 输入文件不存在，无法继续处理")
        return False

    try:
        print("🚀 开始预处理...")
        # 调用预处理函数
        clean_sleep_edf(dataset_config=dataset_config, dataset_type=20)
        print("✅ 预处理完成！")
        return True

    except Exception as e:
        print(f"❌ 预处理失败: {str(e)}")
        import traceback

        traceback.print_exc()
        return False


def analyze_output():
    """分析输出结果"""
    print("\n=== 分析预处理结果 ===")

    output_dir = "test_output/"
    if not os.path.exists(output_dir):
        print("❌ 输出目录不存在")
        return

    # 查找生成的文件
    output_files = [f for f in os.listdir(output_dir) if f.endswith(".npz")]
    print(f"生成的文件数量: {len(output_files)}")

    for filename in output_files:
        filepath = os.path.join(output_dir, filename)
        print(f"\n📁 文件: {filename}")
        print(f"   大小: {os.path.getsize(filepath) / 1024 / 1024:.2f} MB")

        # 加载并分析文件内容
        try:
            data = np.load(filepath, allow_pickle=True)
            print(f"   包含的数组:")
            for key in data.files:
                arr = data[key]
                if hasattr(arr, "shape"):
                    print(f"     {key}: shape={arr.shape}, dtype={arr.dtype}")
                else:
                    print(f"     {key}: {type(arr)} = {arr}")

            # 如果有信号数据，显示基本统计
            if "x" in data.files:
                x = data["x"]
                print(f"   信号统计:")
                print(f"     数值范围: [{x.min():.2f}, {x.max():.2f}]")
                print(f"     均值: {x.mean():.3f}, 标准差: {x.std():.3f}")

            # 如果有标签数据，显示分布
            if "y" in data.files:
                y = data["y"]
                unique, counts = np.unique(y, return_counts=True)
                print(f"   标签分布:")
                sleep_stages = {
                    0: "Wake",
                    1: "N1",
                    2: "N2",
                    3: "N3",
                    4: "REM",
                    5: "Unknown",
                }
                for label, count in zip(unique, counts):
                    stage_name = sleep_stages.get(int(label), f"Label_{int(label)}")
                    percentage = count / len(y) * 100
                    print(f"     {stage_name}: {count} epochs ({percentage:.1f}%)")

        except Exception as e:
            print(f"   ❌ 无法加载文件: {str(e)}")


if __name__ == "__main__":
    # 运行测试
    success = test_single_subject()

    if success:
        analyze_output()
    else:
        print("预处理失败，无法分析结果")
