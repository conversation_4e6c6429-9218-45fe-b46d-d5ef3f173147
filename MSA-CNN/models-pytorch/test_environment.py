#!/usr/bin/env python3
"""
Test script to verify the MSA-CNN environment is properly configured.
"""

import sys
import traceback

def test_import(module_name, description):
    """Test importing a module and print result."""
    try:
        __import__(module_name)
        print(f"✓ {description}: OK")
        return True
    except Exception as e:
        print(f"✗ {description}: FAILED - {e}")
        return False

def test_torch_cuda():
    """Test PyTorch CUDA availability."""
    try:
        import torch
        print(f"✓ PyTorch version: {torch.__version__}")
        print(f"✓ CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"✓ CUDA version: {torch.version.cuda}")
            print(f"✓ GPU count: {torch.cuda.device_count()}")
        return True
    except Exception as e:
        print(f"✗ PyTorch/CUDA test failed: {e}")
        return False

def main():
    """Run all environment tests."""
    print("MSA-CNN Environment Test")
    print("=" * 50)
    
    # Test basic packages
    tests = [
        ("numpy", "NumPy"),
        ("pandas", "Pandas"),
        ("matplotlib", "Matplotlib"),
        ("sklearn", "Scikit-learn"),
        ("scipy", "SciPy"),
        ("mne", "MNE"),
        ("torch", "PyTorch"),
        ("ptflops", "PTFlops"),
        ("tqdm", "TQDM"),
    ]
    
    passed = 0
    total = len(tests)
    
    for module, desc in tests:
        if test_import(module, desc):
            passed += 1
    
    print("\n" + "=" * 50)
    
    # Test PyTorch CUDA
    if test_torch_cuda():
        passed += 1
    total += 1
    
    # Test data_handler
    print("\nTesting data_handler package:")
    try:
        import data_handler
        print("✓ data_handler: OK")
        passed += 1
    except Exception as e:
        print(f"✗ data_handler: FAILED - {e}")
        traceback.print_exc()
    total += 1
    
    print("\n" + "=" * 50)
    print(f"Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Environment is ready.")
        return 0
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
