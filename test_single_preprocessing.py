#!/usr/bin/env python3
"""
测试MSA-CNN数据预处理脚本 - 单文件版本
处理单个受试者数据：SC4002E0
"""

import os
import sys
import numpy as np
from datetime import datetime
from mne.io import read_raw_edf

# 添加data_handler到Python路径
sys.path.append("MSA-CNN/data_handler")
from cleaning import dhedfreader

# 从原脚本复制的常量和映射
W = 0
N1 = 1
N2 = 2
N3 = 3
REM = 4
UNKNOWN = 5

ann2label = {
    "Sleep stage W": 0,
    "Sleep stage 1": 1,
    "Sleep stage 2": 2,
    "Sleep stage 3": 3,
    "Sleep stage 4": 3,
    "Sleep stage R": 4,
    "Sleep stage ?": 5,
    "Movement time": 5,
}

EPOCH_SEC_SIZE = 30


def process_single_subject(subject_id="SC4002E0"):
    """处理单个受试者数据"""

    print("=== 测试MSA-CNN数据预处理流程 ===")
    print(f"目标受试者: {subject_id}")
    print()

    # 配置数据路径
    data_dir = "MSA-CNN/data_handler/data/Sleep_EDF_20/edf_files/"
    output_dir = "test_output/"

    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    print(f"输入目录: {data_dir}")
    print(f"输出目录: {output_dir}")
    print()

    # 检查输入文件
    psg_file = os.path.join(data_dir, f"{subject_id}-PSG.edf")
    ann_file = os.path.join(
        data_dir, f"{subject_id[:-2]}EC-Hypnogram.edf"
    )  # SC4002E0 -> SC4002EC

    print("检查输入文件:")
    print(f"PSG文件: {psg_file}")
    print(f"  存在: {os.path.exists(psg_file)}")
    if os.path.exists(psg_file):
        print(f"  大小: {os.path.getsize(psg_file) / 1024 / 1024:.1f} MB")

    print(f"Hypnogram文件: {ann_file}")
    print(f"  存在: {os.path.exists(ann_file)}")
    if os.path.exists(ann_file):
        print(f"  大小: {os.path.getsize(ann_file) / 1024:.1f} KB")
    print()

    if not (os.path.exists(psg_file) and os.path.exists(ann_file)):
        print("❌ 输入文件不存在，无法继续处理")
        return False

    try:
        print("🚀 开始预处理...")

        # 读取PSG文件
        print("读取PSG文件...")
        raw = read_raw_edf(psg_file, preload=True, stim_channel=None)
        sampling_rate = raw.info["sfreq"]
        print(f"采样率: {sampling_rate} Hz")

        # 选择相关的EEG/EOG/EMG通道
        all_channels = raw.info["ch_names"]
        target_channels = ["EEG Fpz-Cz", "EEG Pz-Oz", "EOG horizontal", "EMG submental"]

        # 找到实际存在的目标通道
        select_ch = [ch for ch in target_channels if ch in all_channels]
        print(f"所有通道: {all_channels}")
        print(f"选择的通道: {select_ch}")

        if not select_ch:
            print("❌ 没有找到目标通道")
            return False

        # 获取信号数据（MNE已经自动转换为物理单位）
        raw_ch_df = raw.to_data_frame(time_format=None)[select_ch]
        raw_ch_df.set_index(np.arange(len(raw_ch_df)))
        print(f"信号数据形状: {raw_ch_df.shape}")

        # 检查信号幅值范围
        for ch in select_ch:
            ch_data = raw_ch_df[ch].values
            print(f"  {ch}: 范围[{ch_data.min():.2f}, {ch_data.max():.2f}] μV")

        # 读取PSG文件头
        print("读取PSG文件头...")
        with open(psg_file, "r", errors="ignore") as f:
            reader_raw = dhedfreader.BaseEDFReader(f)
            reader_raw.read_header()
            h_raw = reader_raw.header
        raw_start_dt = datetime.strptime(h_raw["date_time"], "%Y-%m-%d %H:%M:%S")
        print(f"PSG开始时间: {raw_start_dt}")

        # 读取标注文件
        print("读取Hypnogram文件...")
        with open(ann_file, "r", errors="ignore") as f:
            reader_ann = dhedfreader.BaseEDFReader(f)
            reader_ann.read_header()
            h_ann = reader_ann.header
            _, _, ann = zip(*reader_ann.records())
        ann_start_dt = datetime.strptime(h_ann["date_time"], "%Y-%m-%d %H:%M:%S")
        print(f"Hypnogram开始时间: {ann_start_dt}")

        # 验证时间一致性
        if raw_start_dt != ann_start_dt:
            print(f"⚠️  警告: PSG和Hypnogram开始时间不一致")
            print(f"   PSG: {raw_start_dt}")
            print(f"   Hypnogram: {ann_start_dt}")

        # 处理标注并生成标签
        print("处理睡眠分期标注...")
        labels = []
        label_idx = []

        for a in ann[0]:
            onset_sec, duration_sec, ann_char = a
            ann_str = "".join(ann_char)
            label = ann2label.get(ann_str, UNKNOWN)

            if label != UNKNOWN:
                if duration_sec % EPOCH_SEC_SIZE != 0:
                    print(f"⚠️  警告: 持续时间 {duration_sec}s 不是30s的整数倍")
                    continue

                duration_epoch = int(duration_sec / EPOCH_SEC_SIZE)
                label_epoch = np.ones(duration_epoch, dtype=int) * label
                labels.append(label_epoch)

                idx = int(onset_sec * sampling_rate) + np.arange(
                    duration_sec * sampling_rate, dtype=int
                )
                label_idx.append(idx)

                print(
                    f"包含 onset:{onset_sec}s, duration:{duration_sec}s, label:{label} ({ann_str})"
                )
            else:
                print(
                    f"跳过 onset:{onset_sec}s, duration:{duration_sec}s, label:{label} ({ann_str})"
                )

        if not labels:
            print("❌ 没有找到有效的睡眠分期标签")
            return False

        labels = np.hstack(labels)
        label_idx = np.hstack(label_idx)

        print(f"总标签数: {len(labels)} epochs")
        print(f"标签分布: {np.bincount(labels)}")

        # 选择有标签的数据
        print("选择有标签的信号数据...")
        raw_ch = raw_ch_df.values
        raw_ch = raw_ch[label_idx]

        print(f"选择后的信号形状: {raw_ch.shape}")
        print(f"标签形状: {labels.shape}")

        # 重新整形为epochs
        n_epochs = len(labels)
        epoch_len = int(EPOCH_SEC_SIZE * sampling_rate)
        n_channels = raw_ch.shape[1]

        # 重新整形数据
        raw_ch = raw_ch[: n_epochs * epoch_len]
        raw_ch = raw_ch.reshape(n_epochs, epoch_len, n_channels)

        print(f"最终数据形状: {raw_ch.shape}")
        print(f"最终标签形状: {labels.shape}")

        # 保存结果
        output_file = os.path.join(output_dir, f"{subject_id}-new.npz")

        # 如果只有一个通道，保存为单通道格式（与AttnSleep一致）
        if len(select_ch) == 1:
            raw_ch = raw_ch[:, :, 0:1]  # 保持3D形状但只有1个通道
            ch_label_save = select_ch[0]  # 保存为字符串
        else:
            ch_label_save = select_ch  # 保存为列表

        save_dict = {
            "x": raw_ch.astype(np.float32),
            "y": labels.astype(np.int32),
            "fs": sampling_rate,
            "ch_label": ch_label_save,
            "header_raw": h_raw,
            "header_annotation": h_ann,
        }

        np.savez(output_file, **save_dict)
        print(f"✅ 预处理完成！保存到: {output_file}")

        return True

    except Exception as e:
        print(f"❌ 预处理失败: {str(e)}")
        import traceback

        traceback.print_exc()
        return False


def analyze_output():
    """分析输出结果"""
    print("\n=== 分析预处理结果 ===")

    output_dir = "test_output/"
    if not os.path.exists(output_dir):
        print("❌ 输出目录不存在")
        return

    # 查找生成的文件
    output_files = [f for f in os.listdir(output_dir) if f.endswith(".npz")]
    print(f"生成的文件数量: {len(output_files)}")

    for filename in output_files:
        filepath = os.path.join(output_dir, filename)
        print(f"\n📁 文件: {filename}")
        print(f"   大小: {os.path.getsize(filepath) / 1024 / 1024:.2f} MB")

        # 加载并分析文件内容
        try:
            data = np.load(filepath, allow_pickle=True)
            print(f"   包含的数组:")
            for key in data.files:
                arr = data[key]
                if hasattr(arr, "shape"):
                    print(f"     {key}: shape={arr.shape}, dtype={arr.dtype}")
                else:
                    print(f"     {key}: {type(arr)} = {arr}")

            # 如果有信号数据，显示基本统计
            if "x" in data.files:
                x = data["x"]
                print(f"   信号统计:")
                print(f"     数值范围: [{x.min():.2f}, {x.max():.2f}]")
                print(f"     均值: {x.mean():.3f}, 标准差: {x.std():.3f}")

            # 如果有标签数据，显示分布
            if "y" in data.files:
                y = data["y"]
                unique, counts = np.unique(y, return_counts=True)
                print(f"   标签分布:")
                sleep_stages = {
                    0: "Wake",
                    1: "N1",
                    2: "N2",
                    3: "N3",
                    4: "REM",
                    5: "Unknown",
                }
                for label, count in zip(unique, counts):
                    stage_name = sleep_stages.get(int(label), f"Label_{int(label)}")
                    percentage = count / len(y) * 100
                    print(f"     {stage_name}: {count} epochs ({percentage:.1f}%)")

        except Exception as e:
            print(f"   ❌ 无法加载文件: {str(e)}")


if __name__ == "__main__":
    # 运行测试
    success = process_single_subject("SC4002E0")

    if success:
        analyze_output()
    else:
        print("预处理失败，无法分析结果")
